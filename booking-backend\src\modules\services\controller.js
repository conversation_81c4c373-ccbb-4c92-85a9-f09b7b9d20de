/**
 * Services Controller
 * HTTP request handlers for service management
 */

const ServicesService = require('./service');
const { 
  successResponse, 
  createdResponse, 
  paginatedResponse,
  errorResponse,
  validationErrorResponse 
} = require('../../utils/response');
const { validationResult } = require('express-validator');
const { getPaginationParams, buildSortOrder } = require('../../utils/pagination');
const logger = require('../../utils/logger');

class ServicesController {
  /**
   * Get all services with pagination and filters
   * GET /api/services
   */
  async getServices(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { page, limit } = getPaginationParams(req.query);
      const { category, branchId, isActive, isPopular, search, minPrice, maxPrice, sort } = req.query;

      const filters = { category, branchId, isActive, isPopular, search, minPrice, maxPrice };
      const pagination = { 
        page, 
        limit,
        order: buildSortOrder(sort, ['name', 'category', 'price', 'duration', 'createdAt'], [['sortOrder', 'ASC'], ['name', 'ASC']])
      };

      const result = await ServicesService.getServices(filters, pagination);

      logger.info('Services retrieved successfully', {
        userId: req.user.id,
        filters,
        pagination: { page, limit },
        resultCount: result.data.length
      });

      return paginatedResponse(res, result.data, result.pagination, 'Services retrieved successfully');

    } catch (error) {
      logger.error('Get services controller error:', error);
      next(error);
    }
  }

  /**
   * Get service by ID
   * GET /api/services/:id
   */
  async getServiceById(req, res, next) {
    try {
      const { id } = req.params;
      const service = await ServicesService.getServiceById(parseInt(id));

      logger.info('Service retrieved successfully', {
        userId: req.user.id,
        serviceId: id
      });

      return successResponse(res, service, 'Service retrieved successfully');

    } catch (error) {
      logger.error('Get service by ID controller error:', error);
      next(error);
    }
  }

  /**
   * Create new service
   * POST /api/services
   */
  async createService(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const serviceData = req.body;
      const service = await ServicesService.createService(serviceData);

      logger.info('Service created successfully', {
        userId: req.user.id,
        serviceId: service.id,
        serviceName: service.name,
        category: service.category
      });

      return createdResponse(res, service, 'Service created successfully');

    } catch (error) {
      logger.error('Create service controller error:', error);
      next(error);
    }
  }

  /**
   * Update service
   * PUT /api/services/:id
   */
  async updateService(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const updateData = req.body;
      
      const service = await ServicesService.updateService(parseInt(id), updateData);

      logger.info('Service updated successfully', {
        userId: req.user.id,
        serviceId: id,
        updatedFields: Object.keys(updateData)
      });

      return successResponse(res, service, 'Service updated successfully');

    } catch (error) {
      logger.error('Update service controller error:', error);
      next(error);
    }
  }

  /**
   * Delete service
   * DELETE /api/services/:id
   */
  async deleteService(req, res, next) {
    try {
      const { id } = req.params;
      const result = await ServicesService.deleteService(parseInt(id));

      logger.info('Service deleted successfully', {
        userId: req.user.id,
        serviceId: id
      });

      return successResponse(res, result, 'Service deleted successfully');

    } catch (error) {
      logger.error('Delete service controller error:', error);
      next(error);
    }
  }

  /**
   * Toggle service status
   * PATCH /api/services/:id/status
   */
  async toggleServiceStatus(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { isActive } = req.body;

      const service = await ServicesService.toggleServiceStatus(parseInt(id), isActive);

      logger.info('Service status changed successfully', {
        userId: req.user.id,
        serviceId: id,
        newStatus: isActive ? 'active' : 'inactive'
      });

      return successResponse(res, service, `Service ${isActive ? 'activated' : 'deactivated'} successfully`);

    } catch (error) {
      logger.error('Toggle service status controller error:', error);
      next(error);
    }
  }

  /**
   * Get services by category
   * GET /api/services/category/:category
   */
  async getServicesByCategory(req, res, next) {
    try {
      const { category } = req.params;
      const { limit = 50 } = req.query;

      const services = await ServicesService.getServicesByCategory(category, { 
        limit: parseInt(limit)
      });

      logger.info('Services by category retrieved successfully', {
        userId: req.user.id,
        category,
        resultCount: services.length
      });

      return successResponse(res, services, `${category} services retrieved successfully`);

    } catch (error) {
      logger.error('Get services by category controller error:', error);
      next(error);
    }
  }

  /**
   * Get services by branch
   * GET /api/services/branch/:branchId
   */
  async getServicesByBranch(req, res, next) {
    try {
      const { branchId } = req.params;
      const { limit = 100 } = req.query;

      const services = await ServicesService.getServicesByBranch(parseInt(branchId), { 
        limit: parseInt(limit)
      });

      logger.info('Services by branch retrieved successfully', {
        userId: req.user.id,
        branchId,
        resultCount: services.length
      });

      return successResponse(res, services, 'Branch services retrieved successfully');

    } catch (error) {
      logger.error('Get services by branch controller error:', error);
      next(error);
    }
  }

  /**
   * Get popular services
   * GET /api/services/popular
   */
  async getPopularServices(req, res, next) {
    try {
      const { limit = 20 } = req.query;
      const services = await ServicesService.getPopularServices({ 
        limit: parseInt(limit) 
      });

      logger.info('Popular services retrieved successfully', {
        userId: req.user.id,
        resultCount: services.length
      });

      return successResponse(res, services, 'Popular services retrieved successfully');

    } catch (error) {
      logger.error('Get popular services controller error:', error);
      next(error);
    }
  }

  /**
   * Search services
   * GET /api/services/search
   */
  async searchServices(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { q: searchTerm, limit = 20 } = req.query;

      const services = await ServicesService.searchServices(searchTerm, {
        limit: parseInt(limit)
      });

      logger.info('Service search completed', {
        userId: req.user.id,
        searchTerm,
        resultCount: services.length
      });

      return successResponse(res, services, 'Search completed successfully');

    } catch (error) {
      logger.error('Search services controller error:', error);
      next(error);
    }
  }

  /**
   * Get services by price range
   * GET /api/services/price-range
   */
  async getServicesByPriceRange(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { minPrice, maxPrice, limit = 50 } = req.query;

      const services = await ServicesService.getServicesByPriceRange(
        parseFloat(minPrice), 
        parseFloat(maxPrice),
        { limit: parseInt(limit) }
      );

      logger.info('Services by price range retrieved', {
        userId: req.user.id,
        minPrice,
        maxPrice,
        resultCount: services.length
      });

      return successResponse(res, services, 'Services in price range retrieved successfully');

    } catch (error) {
      logger.error('Get services by price range controller error:', error);
      next(error);
    }
  }

  /**
   * Get service statistics
   * GET /api/services/stats
   */
  async getServiceStats(req, res, next) {
    try {
      const stats = await ServicesService.getServiceStats();

      logger.info('Service stats retrieved successfully', {
        userId: req.user.id
      });

      return successResponse(res, stats, 'Service statistics retrieved successfully');

    } catch (error) {
      logger.error('Get service stats controller error:', error);
      next(error);
    }
  }

  /**
   * Get active services (simple list)
   * GET /api/services/active
   */
  async getActiveServices(req, res, next) {
    try {
      const { limit = 100 } = req.query;
      const services = await ServicesService.getActiveServices({ 
        limit: parseInt(limit) 
      });

      logger.info('Active services retrieved successfully', {
        userId: req.user.id,
        resultCount: services.length
      });

      return successResponse(res, services, 'Active services retrieved successfully');

    } catch (error) {
      logger.error('Get active services controller error:', error);
      next(error);
    }
  }

  /**
   * Update service images
   * POST /api/services/:id/images
   */
  async updateServiceImages(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { images } = req.body;

      const service = await ServicesService.updateServiceImages(parseInt(id), images);

      logger.info('Service images updated successfully', {
        userId: req.user.id,
        serviceId: id,
        imageCount: images.length
      });

      return successResponse(res, service, 'Service images updated successfully');

    } catch (error) {
      logger.error('Update service images controller error:', error);
      next(error);
    }
  }

  /**
   * Toggle service popularity
   * PATCH /api/services/:id/popularity
   */
  async toggleServicePopularity(req, res, next) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return validationErrorResponse(res, errors.array());
      }

      const { id } = req.params;
      const { isPopular } = req.body;

      const service = await ServicesService.toggleServicePopularity(parseInt(id), isPopular);

      logger.info('Service popularity changed successfully', {
        userId: req.user.id,
        serviceId: id,
        isPopular
      });

      return successResponse(res, service, `Service ${isPopular ? 'marked as popular' : 'unmarked as popular'} successfully`);

    } catch (error) {
      logger.error('Toggle service popularity controller error:', error);
      next(error);
    }
  }
}

module.exports = new ServicesController();
