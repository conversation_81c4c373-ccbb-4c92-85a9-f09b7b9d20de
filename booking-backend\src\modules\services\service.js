/**
 * Services Service
 * Business logic for service management
 */

const Service = require('./model');
const Branch = require('../branches/model');
const { AppError } = require('../../middleware/errorHandler');
const { paginate } = require('../../utils/pagination');
const logger = require('../../utils/logger');
const { SERVICE_CATEGORIES } = require('../../utils/constants');

class ServicesService {
  /**
   * Get all services with pagination and filters
   * @param {Object} filters - Filter criteria
   * @param {Object} pagination - Pagination options
   * @returns {Promise<Object>} Paginated services
   */
  async getServices(filters = {}, pagination = {}) {
    try {
      const { category, branchId, isActive, isPopular, search, minPrice, maxPrice } = filters;
      const { Op } = require('sequelize');
      const whereClause = {};

      // Apply filters
      if (category && Object.values(SERVICE_CATEGORIES).includes(category)) {
        whereClause.category = category;
      }

      if (branchId) {
        whereClause[Op.or] = [
          { branchId: parseInt(branchId) },
          { branchId: null } // Services available at all branches
        ];
      }

      if (isActive !== undefined) {
        whereClause.isActive = isActive === 'true';
      }

      if (isPopular === 'true') {
        whereClause.isPopular = true;
      }

      if (minPrice || maxPrice) {
        whereClause.price = {};
        if (minPrice) whereClause.price[Op.gte] = parseFloat(minPrice);
        if (maxPrice) whereClause.price[Op.lte] = parseFloat(maxPrice);
      }

      // Search functionality
      if (search) {
        whereClause[Op.or] = [
          { name: { [Op.like]: `%${search}%` } },
          { description: { [Op.like]: `%${search}%` } },
          { category: { [Op.like]: `%${search}%` } }
        ];
      }

      const options = {
        ...pagination,
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ],
        order: [['sortOrder', 'ASC'], ['name', 'ASC']]
      };

      return await paginate(Service, whereClause, options);

    } catch (error) {
      logger.error('Get services failed:', error);
      throw error;
    }
  }

  /**
   * Get service by ID
   * @param {number} serviceId - Service ID
   * @returns {Promise<Object>} Service data
   */
  async getServiceById(serviceId) {
    try {
      const service = await Service.findByPk(serviceId, {
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'address', 'city', 'phone']
          }
        ]
      });

      if (!service) {
        throw new AppError('Service not found', 404, 'SERVICE_NOT_FOUND');
      }

      return service;

    } catch (error) {
      logger.error('Get service by ID failed:', error);
      throw error;
    }
  }

  /**
   * Create new service
   * @param {Object} serviceData - Service data
   * @returns {Promise<Object>} Created service
   */
  async createService(serviceData) {
    try {
      const { name, branchId, category } = serviceData;

      // Check if service name already exists in the same branch
      const whereClause = { name };
      if (branchId) {
        whereClause.branchId = branchId;
      } else {
        whereClause.branchId = null;
      }

      const existingService = await Service.findOne({
        where: whereClause
      });

      if (existingService) {
        throw new AppError('Service with this name already exists in this branch', 409, 'SERVICE_NAME_EXISTS');
      }

      // Validate category
      if (!Object.values(SERVICE_CATEGORIES).includes(category)) {
        throw new AppError('Invalid service category', 400, 'INVALID_CATEGORY');
      }

      // Validate branch if provided
      if (branchId) {
        const branch = await Branch.findByPk(branchId);
        if (!branch || !branch.isActive) {
          throw new AppError('Branch not found or inactive', 404, 'BRANCH_NOT_FOUND');
        }
      }

      // Validate price logic
      if (serviceData.discountPrice && serviceData.discountPrice >= serviceData.price) {
        throw new AppError('Discount price must be less than regular price', 400, 'INVALID_DISCOUNT_PRICE');
      }

      // Validate age restrictions
      if (serviceData.minAge && serviceData.maxAge && serviceData.minAge > serviceData.maxAge) {
        throw new AppError('Minimum age cannot be greater than maximum age', 400, 'INVALID_AGE_RANGE');
      }

      // Create service
      const service = await Service.create(serviceData);

      logger.business('service_created', {
        serviceId: service.id,
        serviceName: service.name,
        category: service.category,
        branchId: service.branchId,
        price: service.price
      });

      return await this.getServiceById(service.id);

    } catch (error) {
      logger.error('Create service failed:', error);
      throw error;
    }
  }

  /**
   * Update service
   * @param {number} serviceId - Service ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated service
   */
  async updateService(serviceId, updateData) {
    try {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new AppError('Service not found', 404, 'SERVICE_NOT_FOUND');
      }

      // Check name uniqueness if updating name or branch
      if ((updateData.name && updateData.name !== service.name) || 
          (updateData.branchId !== undefined && updateData.branchId !== service.branchId)) {
        
        const whereClause = { 
          name: updateData.name || service.name,
          id: { [require('sequelize').Op.ne]: serviceId }
        };

        if (updateData.branchId !== undefined) {
          whereClause.branchId = updateData.branchId;
        } else {
          whereClause.branchId = service.branchId;
        }

        const existingService = await Service.findOne({ where: whereClause });
        if (existingService) {
          throw new AppError('Service with this name already exists in this branch', 409, 'SERVICE_NAME_EXISTS');
        }
      }

      // Validate category if updating
      if (updateData.category && !Object.values(SERVICE_CATEGORIES).includes(updateData.category)) {
        throw new AppError('Invalid service category', 400, 'INVALID_CATEGORY');
      }

      // Validate branch if updating
      if (updateData.branchId) {
        const branch = await Branch.findByPk(updateData.branchId);
        if (!branch || !branch.isActive) {
          throw new AppError('Branch not found or inactive', 404, 'BRANCH_NOT_FOUND');
        }
      }

      // Validate price logic if updating prices
      const newPrice = updateData.price !== undefined ? updateData.price : service.price;
      const newDiscountPrice = updateData.discountPrice !== undefined ? updateData.discountPrice : service.discountPrice;
      
      if (newDiscountPrice && newDiscountPrice >= newPrice) {
        throw new AppError('Discount price must be less than regular price', 400, 'INVALID_DISCOUNT_PRICE');
      }

      // Validate age restrictions if updating
      const newMinAge = updateData.minAge !== undefined ? updateData.minAge : service.minAge;
      const newMaxAge = updateData.maxAge !== undefined ? updateData.maxAge : service.maxAge;
      
      if (newMinAge && newMaxAge && newMinAge > newMaxAge) {
        throw new AppError('Minimum age cannot be greater than maximum age', 400, 'INVALID_AGE_RANGE');
      }

      await service.update(updateData);

      logger.business('service_updated', {
        serviceId: service.id,
        serviceName: service.name,
        updatedFields: Object.keys(updateData)
      });

      return await this.getServiceById(service.id);

    } catch (error) {
      logger.error('Update service failed:', error);
      throw error;
    }
  }

  /**
   * Delete service (soft delete)
   * @param {number} serviceId - Service ID
   * @returns {Promise<Object>} Success message
   */
  async deleteService(serviceId) {
    try {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new AppError('Service not found', 404, 'SERVICE_NOT_FOUND');
      }

      // Check if service has active bookings
      // TODO: Implement booking check when Booking model is available
      // const activeBookings = await Booking.count({
      //   where: {
      //     serviceId,
      //     status: { [Op.in]: ['pending', 'confirmed'] }
      //   }
      // });

      // if (activeBookings > 0) {
      //   throw new AppError('Cannot delete service with active bookings', 400, 'SERVICE_HAS_ACTIVE_BOOKINGS');
      // }

      // Soft delete by setting isActive to false
      await service.update({ isActive: false });

      logger.business('service_deleted', {
        serviceId: service.id,
        serviceName: service.name
      });

      return { message: 'Service deleted successfully' };

    } catch (error) {
      logger.error('Delete service failed:', error);
      throw error;
    }
  }

  /**
   * Toggle service status
   * @param {number} serviceId - Service ID
   * @param {boolean} isActive - Active status
   * @returns {Promise<Object>} Updated service
   */
  async toggleServiceStatus(serviceId, isActive) {
    try {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new AppError('Service not found', 404, 'SERVICE_NOT_FOUND');
      }

      await service.update({ isActive });

      logger.business('service_status_changed', {
        serviceId: service.id,
        serviceName: service.name,
        newStatus: isActive ? 'active' : 'inactive'
      });

      return await this.getServiceById(service.id);

    } catch (error) {
      logger.error('Toggle service status failed:', error);
      throw error;
    }
  }

  /**
   * Get services by category
   * @param {string} category - Service category
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Services in the category
   */
  async getServicesByCategory(category, options = {}) {
    try {
      if (!Object.values(SERVICE_CATEGORIES).includes(category)) {
        throw new AppError('Invalid service category', 400, 'INVALID_CATEGORY');
      }

      return await Service.getServicesByCategory(category, {
        ...options,
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get services by category failed:', error);
      throw error;
    }
  }

  /**
   * Get services by branch
   * @param {number} branchId - Branch ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Services available at the branch
   */
  async getServicesByBranch(branchId, options = {}) {
    try {
      const branch = await Branch.findByPk(branchId);
      if (!branch) {
        throw new AppError('Branch not found', 404, 'BRANCH_NOT_FOUND');
      }

      return await Service.getServicesByBranch(branchId, {
        ...options,
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get services by branch failed:', error);
      throw error;
    }
  }

  /**
   * Get popular services
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Popular services
   */
  async getPopularServices(options = {}) {
    try {
      return await Service.getPopularServices({
        ...options,
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get popular services failed:', error);
      throw error;
    }
  }

  /**
   * Search services
   * @param {string} searchTerm - Search term
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Matching services
   */
  async searchServices(searchTerm, options = {}) {
    try {
      if (!searchTerm || searchTerm.trim().length < 2) {
        throw new AppError('Search term must be at least 2 characters', 400, 'INVALID_SEARCH_TERM');
      }

      return await Service.searchServices(searchTerm.trim(), {
        ...options,
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Search services failed:', error);
      throw error;
    }
  }

  /**
   * Get services by price range
   * @param {number} minPrice - Minimum price
   * @param {number} maxPrice - Maximum price
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Services in price range
   */
  async getServicesByPriceRange(minPrice, maxPrice, options = {}) {
    try {
      if (minPrice < 0 || maxPrice < 0 || minPrice > maxPrice) {
        throw new AppError('Invalid price range', 400, 'INVALID_PRICE_RANGE');
      }

      return await Service.getServicesByPriceRange(minPrice, maxPrice, {
        ...options,
        include: [
          {
            model: Branch,
            as: 'branch',
            attributes: ['id', 'name', 'city'],
            required: false
          }
        ]
      });

    } catch (error) {
      logger.error('Get services by price range failed:', error);
      throw error;
    }
  }

  /**
   * Get service statistics
   * @returns {Promise<Object>} Service statistics
   */
  async getServiceStats() {
    try {
      const { Op } = require('sequelize');
      const sequelize = require('../../database/connection').sequelize;

      const totalServices = await Service.count({ where: { isActive: true } });

      const servicesByCategory = await Service.findAll({
        attributes: [
          'category',
          [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: { isActive: true },
        group: ['category'],
        raw: true
      });

      const popularServices = await Service.count({
        where: { isActive: true, isPopular: true }
      });

      const newServicesThisMonth = await Service.count({
        where: {
          isActive: true,
          createdAt: {
            [Op.gte]: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      });

      const averagePrice = await Service.findOne({
        attributes: [
          [sequelize.fn('AVG', sequelize.col('price')), 'avgPrice']
        ],
        where: { isActive: true },
        raw: true
      });

      const priceRange = await Service.findOne({
        attributes: [
          [sequelize.fn('MIN', sequelize.col('price')), 'minPrice'],
          [sequelize.fn('MAX', sequelize.col('price')), 'maxPrice']
        ],
        where: { isActive: true },
        raw: true
      });

      return {
        total: totalServices,
        popular: popularServices,
        newThisMonth: newServicesThisMonth,
        averagePrice: parseFloat(averagePrice.avgPrice) || 0,
        priceRange: {
          min: parseFloat(priceRange.minPrice) || 0,
          max: parseFloat(priceRange.maxPrice) || 0
        },
        byCategory: servicesByCategory.reduce((acc, service) => {
          acc[service.category] = parseInt(service.count);
          return acc;
        }, {})
      };

    } catch (error) {
      logger.error('Get service stats failed:', error);
      throw error;
    }
  }

  /**
   * Update service images
   * @param {number} serviceId - Service ID
   * @param {Array} imageUrls - Array of image URLs
   * @returns {Promise<Object>} Updated service
   */
  async updateServiceImages(serviceId, imageUrls) {
    try {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new AppError('Service not found', 404, 'SERVICE_NOT_FOUND');
      }

      await service.update({ images: imageUrls });

      logger.business('service_images_updated', {
        serviceId: service.id,
        serviceName: service.name,
        imageCount: imageUrls.length
      });

      return await this.getServiceById(service.id);

    } catch (error) {
      logger.error('Update service images failed:', error);
      throw error;
    }
  }

  /**
   * Toggle service popularity
   * @param {number} serviceId - Service ID
   * @param {boolean} isPopular - Popular status
   * @returns {Promise<Object>} Updated service
   */
  async toggleServicePopularity(serviceId, isPopular) {
    try {
      const service = await Service.findByPk(serviceId);
      if (!service) {
        throw new AppError('Service not found', 404, 'SERVICE_NOT_FOUND');
      }

      await service.update({ isPopular });

      logger.business('service_popularity_changed', {
        serviceId: service.id,
        serviceName: service.name,
        isPopular
      });

      return await this.getServiceById(service.id);

    } catch (error) {
      logger.error('Toggle service popularity failed:', error);
      throw error;
    }
  }

  /**
   * Get active services (simple list)
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Active services
   */
  async getActiveServices(options = {}) {
    try {
      return await Service.getActiveServices({
        ...options,
        attributes: ['id', 'name', 'category', 'duration', 'price', 'discountPrice']
      });

    } catch (error) {
      logger.error('Get active services failed:', error);
      throw error;
    }
  }
}

module.exports = new ServicesService();
